---
title: Configuration
description: Learn how to configure your documentation system
---

## Configuration Guide

This guide covers how to configure various aspects of your documentation system.

### Fumadocs Configuration

The main configuration is in `source.config.ts`:

```typescript
import { defineDocs } from 'fumadocs-mdx/config';
import { defineConfig } from 'fumadocs-mdx/config';

// Define the docs collection
export const { docs, meta } = defineDocs({
  dir: 'content/docs',
});

// Global MDX configuration
export default defineConfig({
  mdxOptions: {
    providerImportSource: '@/mdx-components',
    rehypeCodeOptions: {
      themes: {
        light: 'github-light',
        dark: 'github-dark',
      },
    },
  },
});
```

### MDX Components

Configure custom MDX components in `mdx-components.tsx`:

```typescript
import defaultMdxComponents from 'fumadocs-ui/mdx';
import * as TabsComponents from 'fumadocs-ui/components/tabs';
import { CodeBlock, Pre } from 'fumadocs-ui/components/codeblock';
import type { MDXComponents } from 'mdx/types';

export function getMDXComponents(components?: MDXComponents): MDXComponents {
  return {
    ...defaultMdxComponents,
    ...TabsComponents,
    pre: ({ ref: _ref, ...props }) => (
      <CodeBlock {...props}>
        <Pre>{props.children}</Pre>
      </CodeBlock>
    ),
    ...components,
  };
}
```

### Layout Configuration

Configure the base layout options in `src/app/layout.config.tsx`:

```typescript
import { BaseLayoutProps } from 'fumadocs-ui/layouts/shared';

export const baseOptions: BaseLayoutProps = {
  nav: {
    title: 'My Documentation',
    url: '/',
  },
  links: [
    {
      text: 'Documentation',
      url: '/docs',
      active: 'nested-url',
    },
    {
      text: 'GitHub',
      url: 'https://github.com/your-repo',
      external: true,
    },
  ],
};
```

### Theme Configuration

Customize colors in `src/app/globals.css`:

```css
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  /* ... more variables */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --primary: oklch(0.708 0 0);
  --primary-foreground: oklch(0.145 0 0);
  /* ... more variables */
}
```

### Search Configuration

Enable search functionality in `src/app/api/search/route.ts`:

```typescript
import { source } from '@/lib/source';
import { createFromSource } from 'fumadocs-core/search/server';

export const { GET } = createFromSource(source, {
  language: 'english',
});
```

### Next.js Configuration

Configure Next.js with Fumadocs in `next.config.ts`:

```typescript
import type { NextConfig } from "next";
import { createMDX } from 'fumadocs-mdx/next';

const withMDX = createMDX();

const nextConfig: NextConfig = {
  reactStrictMode: true,
};

export default withMDX(nextConfig);
```
