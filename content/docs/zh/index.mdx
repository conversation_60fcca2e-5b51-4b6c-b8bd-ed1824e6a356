---
title: 文档系统
description: 基于 Fumadocs 的现代化文档系统
---

## 欢迎使用文档系统

这是一个基于 **Fumadocs** 构建的现代化文档系统，集成了 Next.js 15、React 19 和 TypeScript。

### 🚀 主要特性

- **现代化 UI**: 使用 Fumadocs UI 构建的美观、响应式设计
- **MDX 支持**: 在 Markdown 中使用 React 组件
- **代码高亮**: 支持多种编程语言的语法高亮
- **搜索功能**: 内置搜索功能
- **深色模式**: 自动深色/浅色模式支持
- **类型安全**: 完整的 TypeScript 支持

### 📁 项目结构

```
├── content/docs/          # 文档内容
│   ├── index.mdx         # 首页
│   ├── getting-started.mdx
│   ├── components.mdx
│   └── configuration.mdx
├── src/
│   ├── app/docs/         # 文档路由
│   ├── lib/source.ts     # 文档源配置
│   └── mdx-components.tsx # MDX 组件配置
├── source.config.ts      # Fumadocs 配置
└── .source/              # 自动生成的类型文件
```

### 🛠 如何启动

1. **安装依赖**:
   ```bash
   pnpm install
   ```

2. **生成文档类型**:
   ```bash
   pnpm fumadocs-mdx
   ```

3. **启动开发服务器**:
   ```bash
   pnpm dev
   ```

4. **访问文档**: 打开 [http://localhost:3001/docs](http://localhost:3001/docs)

### 📝 添加新文档

1. 在 `content/docs/` 目录下创建新的 `.mdx` 文件
2. 添加 frontmatter:
   ```yaml
   ---
   title: 页面标题
   description: 页面描述
   ---
   ```
3. 编写内容
4. 更新 `content/docs/meta.json` 文件以包含新页面

### 🔧 配置说明

- **source.config.ts**: 主要配置文件，定义文档集合和 MDX 选项
- **mdx-components.tsx**: 自定义 MDX 组件
- **src/lib/source.ts**: 文档源加载器配置

### 📚 快速导航

- [开始使用](/docs/getting-started) - 详细的入门指南
- [组件库](/docs/components) - 可用的 UI 组件
- [配置指南](/docs/configuration) - 系统配置说明