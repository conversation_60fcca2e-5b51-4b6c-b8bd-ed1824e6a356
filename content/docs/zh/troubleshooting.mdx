---
title: 故障排除
description: 常见问题和解决方案
---

## 故障排除指南

本指南帮助您解决使用文档系统时可能遇到的常见问题。

### 🔧 常见问题

#### 1. Hydration 错误

**错误信息**:
```
Hydration failed because the server rendered HTML didn't match the client
```

**解决方案**:
- 确保在 `html` 和 `body` 标签上添加了 `suppressHydrationWarning` 属性
- 检查是否有客户端特定的代码在服务器端运行

**修复代码**:
```tsx
// src/app/layout.tsx
<html lang="en" suppressHydrationWarning>
  <body suppressHydrationWarning>
    <RootProvider>{children}</RootProvider>
  </body>
</html>
```

#### 2. 模块解析错误

**错误信息**:
```
Module not found: Can't resolve '@/.source'
```

**解决方案**:
1. 确保运行了 `pnpm fumadocs-mdx` 生成 `.source` 文件夹
2. 检查 `tsconfig.json` 中的路径映射:

```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./src/*"],
      "@/.source": ["./.source"]
    }
  }
}
```

#### 3. MDX 组件未找到

**错误信息**:
```
Module not found: Can't resolve '@/mdx-components'
```

**解决方案**:
确保 `mdx-components.tsx` 文件在正确的位置 (`src/mdx-components.tsx`)

#### 4. 文档页面 404 错误

**可能原因**:
- 文档文件路径不正确
- `meta.json` 配置错误
- 路由配置问题

**解决方案**:
1. 检查文件路径是否正确
2. 验证 `content/docs/meta.json` 配置:
```json
{
  "title": "Documentation",
  "pages": [
    "index",
    "getting-started",
    "components",
    "configuration"
  ]
}
```

### 🚀 启动流程

#### 正确的启动顺序

1. **安装依赖**:
   ```bash
   pnpm install
   ```

2. **生成文档类型** (重要):
   ```bash
   pnpm fumadocs-mdx
   ```
   这会生成 `.source` 文件夹和类型定义

3. **启动开发服务器**:
   ```bash
   pnpm dev
   ```

#### 如果遇到问题

1. **清理并重新生成**:
   ```bash
   # 删除生成的文件
   rm -rf .source
   rm -rf .next
   
   # 重新生成
   pnpm fumadocs-mdx
   pnpm dev
   ```

2. **检查端口冲突**:
   如果端口 3000 被占用，Next.js 会自动使用 3001

### 📝 配置检查清单

确保以下文件配置正确：

#### ✅ source.config.ts
```typescript
import { defineDocs } from 'fumadocs-mdx/config';
import { defineConfig } from 'fumadocs-mdx/config';

export const { docs, meta } = defineDocs({
  dir: 'content/docs',
});

export default defineConfig({
  mdxOptions: {
    providerImportSource: '@/mdx-components',
    rehypeCodeOptions: {
      themes: {
        light: 'github-light',
        dark: 'github-dark',
      },
    },
  },
});
```

#### ✅ src/mdx-components.tsx
```typescript
import defaultMdxComponents from 'fumadocs-ui/mdx';
import * as TabsComponents from 'fumadocs-ui/components/tabs';
import { CodeBlock, Pre } from 'fumadocs-ui/components/codeblock';
import type { MDXComponents } from 'mdx/types';

export function getMDXComponents(components?: MDXComponents): MDXComponents {
  return {
    ...defaultMdxComponents,
    ...TabsComponents,
    pre: ({ ref: _ref, ...props }) => (
      <CodeBlock {...props}>
        <Pre>{props.children}</Pre>
      </CodeBlock>
    ),
    ...components,
  };
}
```

#### ✅ src/lib/source.ts
```typescript
import { docs, meta } from '@/.source';
import { loader } from 'fumadocs-core/source';
import { createMDXSource } from 'fumadocs-mdx';

export const source = loader({
  baseUrl: '/docs',
  source: createMDXSource(docs, meta),
});
```

#### ✅ next.config.ts
```typescript
import type { NextConfig } from "next";
import { createMDX } from 'fumadocs-mdx/next';

const withMDX = createMDX();

const nextConfig: NextConfig = {
  reactStrictMode: true,
};

export default withMDX(nextConfig);
```

### 🔍 调试技巧

1. **检查控制台错误**: 打开浏览器开发者工具查看详细错误信息
2. **验证文件结构**: 确保所有必需的文件都在正确位置
3. **重新生成类型**: 当添加新文档时，运行 `pnpm fumadocs-mdx`
4. **清理缓存**: 删除 `.next` 文件夹并重新启动

### 📞 获取帮助

如果问题仍然存在：

1. 查看 [Fumadocs 官方文档](https://fumadocs.vercel.app/)
2. 检查 GitHub Issues
3. 确保使用的是兼容的版本

### 🎯 成功指标

文档系统正常工作的标志：

- ✅ 开发服务器启动无错误
- ✅ `/docs` 页面正常加载
- ✅ 导航菜单显示正确
- ✅ 代码高亮正常工作
- ✅ 搜索功能可用
- ✅ 深色/浅色模式切换正常
