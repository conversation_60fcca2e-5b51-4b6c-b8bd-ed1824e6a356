---
title: Getting Started
description: Learn how to get started with our documentation system
---

## Welcome to Our Documentation

This is a comprehensive guide to help you get started with our Next.js template and documentation system powered by Fumadocs.

### Features

Our documentation system includes:

- **Modern UI**: Built with Fumadocs UI for a beautiful, responsive design
- **MDX Support**: Write content in MDX with React components
- **Code Highlighting**: Syntax highlighting for multiple languages
- **Search**: Built-in search functionality
- **Dark Mode**: Automatic dark/light mode support

### Installation

To get started, install the dependencies:

```bash
pnpm install
# or
npm install
# or
yarn install
```

### Development

Start the development server:

```bash
pnpm dev
# or
npm run dev
# or
yarn dev
```

### Code Examples

Here's a simple React component example:

```tsx
import React from 'react';

const HelloWorld = () => {
  return (
    <div className="p-4 bg-blue-100 rounded-lg">
      <h1 className="text-2xl font-bold text-blue-800">
        Hello, World!
      </h1>
      <p className="text-blue-600">
        Welcome to our documentation system.
      </p>
    </div>
  );
};

export default HelloWorld;
```

### Next Steps

- Explore the [Components](/docs/components) section
- Learn about [Configuration](/docs/configuration)
- Check out [Examples](/docs/examples)
