---
title: Documentation System
description: Modern documentation system based on Fumadocs
---

## Welcome to Documentation System

This is a modern documentation system built with **Fumadocs**, integrated with Next.js 15, React 19, and TypeScript.

### 🚀 Key Features

- **Modern UI**: Beautiful, responsive design built with Fumadocs UI
- **MDX Support**: Use React components in Markdown
- **Code Highlighting**: Syntax highlighting for multiple programming languages
- **Search Functionality**: Built-in search feature
- **Dark Mode**: Automatic dark/light mode support
- **Type Safety**: Complete TypeScript support

### 📁 Project Structure

```
├── content/docs/          # Documentation content
│   ├── index.mdx         # Homepage
│   ├── getting-started.mdx
│   ├── components.mdx
│   └── configuration.mdx
├── src/
│   ├── app/docs/         # Documentation routes
│   ├── lib/source.ts     # Documentation source configuration
│   └── mdx-components.tsx # MDX component configuration
├── source.config.ts      # Fumadocs configuration
└── .source/              # Auto-generated type files
```

### 🛠 How to Start

1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Generate documentation types**:
   ```bash
   pnpm fumadocs-mdx
   ```

3. **Start development server**:
   ```bash
   pnpm dev
   ```

4. **Visit documentation**: Open [http://localhost:3001/docs](http://localhost:3001/docs)

### 📝 Adding New Documentation

1. Create a new `.mdx` file in the `content/docs/` directory
2. Add frontmatter:
   ```yaml
   ---
   title: Page Title
   description: Page Description
   ---
   ```
3. Write content
4. Update the `content/docs/meta.json` file to include the new page

### 🔧 Configuration Guide

- **source.config.ts**: Main configuration file, defines document collections and MDX options
- **mdx-components.tsx**: Custom MDX components
- **src/lib/source.ts**: Document source loader configuration

### 📚 Quick Navigation

- [Getting Started](/docs/getting-started) - Detailed getting started guide
- [Components](/docs/components) - Available UI components
- [Configuration](/docs/configuration) - System configuration guide