import { source } from '@/lib/source';
import {
  DocsBody,
  DocsDescription,
  DocsPage,
  DocsTitle,
} from 'fumadocs-ui/page';
import { notFound } from 'next/navigation';
import { getMDXComponents } from '@/mdx-components';
import { i18n } from '@/lib/i18n';

export default async function Page(props: {
  params: Promise<{ locale: string; slug?: string[] }>;
}) {
  const params = await props.params;
  
  // For i18n, we need to construct the full path including locale
  const fullSlug = params.slug ? [params.locale, ...params.slug] : [params.locale];
  const page = source.getPage(fullSlug);
  
  if (!page) notFound();

  const MDX = page.data.body;

  return (
    <DocsPage toc={page.data.toc} full={page.data.full}>
      <DocsTitle>{page.data.title}</DocsTitle>
      <DocsDescription>{page.data.description}</DocsDescription>
      <DocsBody>
        <MDX components={getMDXComponents()} />
      </DocsBody>
    </DocsPage>
  );
}

export async function generateStaticParams() {
  const params = source.generateParams();
  
  // Transform the params to extract locale and create proper slug structure
  return params.map((param) => {
    const segments = param.slug || [];
    if (segments.length === 0) return param;
    
    // Check if first segment is a locale
    const firstSegment = segments[0];
    if (i18n.languages.includes(firstSegment)) {
      return {
        locale: firstSegment,
        slug: segments.slice(1).length > 0 ? segments.slice(1) : undefined,
      };
    }
    
    return param;
  });
}

export async function generateMetadata(props: {
  params: Promise<{ locale: string; slug?: string[] }>;
}) {
  const params = await props.params;
  const fullSlug = params.slug ? [params.locale, ...params.slug] : [params.locale];
  const page = source.getPage(fullSlug);
  
  if (!page) notFound();

  return {
    title: page.data.title,
    description: page.data.description,
  };
}