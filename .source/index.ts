// @ts-nocheck -- skip type checking
import * as meta_0 from "../content/docs/meta.json?collection=meta&hash=1752216621405"
import * as docs_3 from "../content/docs/index.zh.mdx?collection=docs&hash=1752216621405"
import * as docs_2 from "../content/docs/index.en.mdx?collection=docs&hash=1752216621405"
import * as docs_1 from "../content/docs/getting-started.zh.mdx?collection=docs&hash=1752216621405"
import * as docs_0 from "../content/docs/getting-started.en.mdx?collection=docs&hash=1752216621405"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.doc<typeof _source.docs>([{ info: {"path":"getting-started.en.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/getting-started.en.mdx"}, data: docs_0 }, { info: {"path":"getting-started.zh.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/getting-started.zh.mdx"}, data: docs_1 }, { info: {"path":"index.en.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/index.en.mdx"}, data: docs_2 }, { info: {"path":"index.zh.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/index.zh.mdx"}, data: docs_3 }]);
export const meta = _runtime.meta<typeof _source.meta>([{ info: {"path":"meta.json","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/meta.json"}, data: meta_0 }]);