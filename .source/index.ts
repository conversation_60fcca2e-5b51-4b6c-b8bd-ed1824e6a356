// @ts-nocheck -- skip type checking
import * as meta_0 from "../content/docs/meta.json?collection=meta&hash=1752214576701"
import * as docs_3 from "../content/docs/index.mdx?collection=docs&hash=1752214576701"
import * as docs_2 from "../content/docs/getting-started.mdx?collection=docs&hash=1752214576701"
import * as docs_1 from "../content/docs/configuration.mdx?collection=docs&hash=1752214576701"
import * as docs_0 from "../content/docs/components.mdx?collection=docs&hash=1752214576701"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.doc<typeof _source.docs>([{ info: {"path":"components.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/components.mdx"}, data: docs_0 }, { info: {"path":"configuration.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/configuration.mdx"}, data: docs_1 }, { info: {"path":"getting-started.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/getting-started.mdx"}, data: docs_2 }, { info: {"path":"index.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/index.mdx"}, data: docs_3 }]);
export const meta = _runtime.meta<typeof _source.meta>([{ info: {"path":"meta.json","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/meta.json"}, data: meta_0 }]);