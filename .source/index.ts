// @ts-nocheck -- skip type checking
import * as meta_1 from "../content/docs/zh/meta.json?collection=meta&hash=1752216621405"
import * as meta_0 from "../content/docs/en/meta.json?collection=meta&hash=1752216621405"
import * as docs_9 from "../content/docs/zh/troubleshooting.mdx?collection=docs&hash=1752216621405"
import * as docs_8 from "../content/docs/zh/index.mdx?collection=docs&hash=1752216621405"
import * as docs_7 from "../content/docs/zh/getting-started.mdx?collection=docs&hash=1752216621405"
import * as docs_6 from "../content/docs/zh/configuration.mdx?collection=docs&hash=1752216621405"
import * as docs_5 from "../content/docs/zh/components.mdx?collection=docs&hash=1752216621405"
import * as docs_4 from "../content/docs/en/troubleshooting.mdx?collection=docs&hash=1752216621405"
import * as docs_3 from "../content/docs/en/index.mdx?collection=docs&hash=1752216621405"
import * as docs_2 from "../content/docs/en/getting-started.mdx?collection=docs&hash=1752216621405"
import * as docs_1 from "../content/docs/en/configuration.mdx?collection=docs&hash=1752216621405"
import * as docs_0 from "../content/docs/en/components.mdx?collection=docs&hash=1752216621405"
import { _runtime } from "fumadocs-mdx"
import * as _source from "../source.config"
export const docs = _runtime.doc<typeof _source.docs>([{ info: {"path":"en/components.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/en/components.mdx"}, data: docs_0 }, { info: {"path":"en/configuration.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/en/configuration.mdx"}, data: docs_1 }, { info: {"path":"en/getting-started.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/en/getting-started.mdx"}, data: docs_2 }, { info: {"path":"en/index.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/en/index.mdx"}, data: docs_3 }, { info: {"path":"en/troubleshooting.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/en/troubleshooting.mdx"}, data: docs_4 }, { info: {"path":"zh/components.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/zh/components.mdx"}, data: docs_5 }, { info: {"path":"zh/configuration.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/zh/configuration.mdx"}, data: docs_6 }, { info: {"path":"zh/getting-started.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/zh/getting-started.mdx"}, data: docs_7 }, { info: {"path":"zh/index.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/zh/index.mdx"}, data: docs_8 }, { info: {"path":"zh/troubleshooting.mdx","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/zh/troubleshooting.mdx"}, data: docs_9 }]);
export const meta = _runtime.meta<typeof _source.meta>([{ info: {"path":"en/meta.json","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/en/meta.json"}, data: meta_0 }, { info: {"path":"zh/meta.json","absolutePath":"/Users/<USER>/fuwenhao/github/nextjs-template-202507/content/docs/zh/meta.json"}, data: meta_1 }]);